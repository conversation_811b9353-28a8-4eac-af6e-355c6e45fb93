<?php

namespace App\Models;

/**
 * Model for the 'districts' table.
 * Represents districts with province reference, code, name, map defaults, and audit columns.
 */
class DistrictModel extends BaseModel
{
    protected $table = 'districts';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = true;
    protected $allowedFields = [
        'province_id', 'dist_code', 'name',
        'map_centre_lat', 'map_centre_lng', 'map_zoom',
        'created_by', 'updated_by', 'deleted_by',
    ];
    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    /**
     * Only essential validation rules: required for province_id, dist_code, and name.
     */
    protected $validationRules = [
        'province_id' => 'required',
        'dist_code'   => 'required',
        'name'        => 'required',
    ];
    protected $validationMessages = [];
    protected $skipValidation = false;
    protected $cleanValidationRules = true;
    protected $allowCallbacks = true;
    protected $beforeInsert   = [];
    protected $afterInsert    = [];
    protected $beforeUpdate   = [];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];
} 