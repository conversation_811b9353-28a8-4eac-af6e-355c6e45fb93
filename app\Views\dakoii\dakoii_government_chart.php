<?= $this->extend('templates/dakoii_portal_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('dakoii/government') ?>" class="btn btn-secondary">
    <i class="icon">←</i> Back to Overview
</a>
<button class="btn btn-primary" onclick="exportChart()">
    <i class="icon">📤</i> Export Chart
</button>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Page Header -->
    <div style="margin-bottom: var(--spacing-xl);">
        <h1 style="color: var(--text-primary); margin-bottom: var(--spacing-sm); font-size: 2rem; display: flex; align-items: center; gap: var(--spacing-md);">
            <span style="font-size: 2.5rem;">📊</span>
            Government Hierarchy Chart
        </h1>
        <p style="color: var(--text-secondary); font-size: 1rem;">
            Interactive visualization of the complete government structure hierarchy.
        </p>
    </div>

    <!-- Chart Controls -->
    <div class="card" style="margin-bottom: var(--spacing-xl);">
        <div class="card-header">Chart Controls</div>
        <div style="display: flex; gap: var(--spacing-md); align-items: center; flex-wrap: wrap;">
            <div style="display: flex; align-items: center; gap: var(--spacing-sm);">
                <label class="form-label" style="margin: 0;">View Level:</label>
                <select id="chartLevel" class="form-input" style="width: auto;" onchange="updateChart()">
                    <option value="all">All Levels</option>
                    <option value="countries">Countries Only</option>
                    <option value="provinces">Countries → Provinces</option>
                    <option value="districts">Countries → Provinces → Districts</option>
                    <option value="llgs">Complete Hierarchy</option>
                </select>
            </div>
            <div style="display: flex; align-items: center; gap: var(--spacing-sm);">
                <label class="form-label" style="margin: 0;">Layout:</label>
                <select id="chartLayout" class="form-input" style="width: auto;" onchange="updateChart()">
                    <option value="tree">Tree View</option>
                    <option value="radial">Radial View</option>
                    <option value="horizontal">Horizontal Tree</option>
                </select>
            </div>
            <button class="btn btn-secondary" onclick="resetZoom()">
                <i class="icon">🔍</i> Reset Zoom
            </button>
            <button class="btn btn-secondary" onclick="expandAll()">
                <i class="icon">📂</i> Expand All
            </button>
            <button class="btn btn-secondary" onclick="collapseAll()">
                <i class="icon">📁</i> Collapse All
            </button>
            <button class="btn btn-secondary" onclick="showSimpleTree()">
                <i class="icon">🌳</i> Simple Tree
            </button>
        </div>
    </div>

    <!-- Hierarchy Chart -->
    <div class="card">
        <div class="card-header">Interactive Hierarchy Chart</div>
        <div id="hierarchy-chart" style="height: 600px; position: relative; overflow: hidden; border-radius: var(--radius-md);">
            <!-- Chart will be rendered here -->
            <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: var(--text-tertiary);">
                <div style="text-align: center;">
                    <div style="font-size: 4rem; margin-bottom: var(--spacing-md);">📊</div>
                    <h3 style="color: var(--text-primary); margin-bottom: var(--spacing-sm);">Loading Hierarchy Chart...</h3>
                    <p>Interactive government structure visualization will appear here.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Chart Legend -->
    <div class="card">
        <div class="card-header">Chart Legend</div>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: var(--spacing-md);">
            <div style="display: flex; align-items: center; gap: var(--spacing-sm);">
                <div style="width: 20px; height: 20px; background: var(--gradient-primary); border-radius: 50%;"></div>
                <span style="color: var(--text-secondary);">Countries</span>
            </div>
            <div style="display: flex; align-items: center; gap: var(--spacing-sm);">
                <div style="width: 20px; height: 20px; background: var(--gradient-secondary); border-radius: 50%;"></div>
                <span style="color: var(--text-secondary);">Provinces</span>
            </div>
            <div style="display: flex; align-items: center; gap: var(--spacing-sm);">
                <div style="width: 20px; height: 20px; background: var(--gradient-accent); border-radius: 50%;"></div>
                <span style="color: var(--text-secondary);">Districts</span>
            </div>
            <div style="display: flex; align-items: center; gap: var(--spacing-sm);">
                <div style="width: 20px; height: 20px; background: #06FFA5; border-radius: 50%;"></div>
                <span style="color: var(--text-secondary);">Local Level Governments</span>
            </div>
        </div>
    </div>
</div>

<script src="https://d3js.org/d3.v7.min.js"></script>
<script>
// Government Hierarchy Chart Implementation
let chartData = null;
let svg = null;
let currentLevel = 'all';
let currentLayout = 'tree';

document.addEventListener('DOMContentLoaded', function() {
    initializeChart();
    loadChartData();
});

function initializeChart() {
    // Chart initialization is now handled in renderTreeChart
    console.log('Chart initialization ready');
}

function loadChartData() {
    // Use real data from the controller
    const countries = <?= json_encode($countries ?? []) ?>;

    console.log('Countries data:', countries);

    // Check if we have data
    if (!countries || countries.length === 0) {
        console.log('No countries data available');
        // Show a message instead of empty chart
        document.getElementById('hierarchy-chart').innerHTML = `
            <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: var(--text-tertiary);">
                <div style="text-align: center;">
                    <div style="font-size: 4rem; margin-bottom: var(--spacing-md);">📊</div>
                    <h3 style="color: var(--text-primary); margin-bottom: var(--spacing-sm);">No Data Available</h3>
                    <p>Please add countries, provinces, districts, and LLGs to view the hierarchy chart.</p>
                    <a href="<?= base_url('dakoii/government/countries/create') ?>" class="btn btn-primary" style="margin-top: var(--spacing-md);">
                        Add Country
                    </a>
                </div>
            </div>
        `;
        return;
    }

    // Create a simple hierarchical structure
    chartData = {
        name: "Government Structure",
        type: "root",
        children: countries.map(country => ({
            name: country.name,
            type: "country",
            id: country.id,
            children: (country.provinces || []).map(province => ({
                name: province.name,
                type: "province",
                id: province.id,
                children: (province.districts || []).map(district => ({
                    name: district.name,
                    type: "district",
                    id: district.id,
                    children: (district.llgs || []).map(llg => ({
                        name: llg.name,
                        type: "llg",
                        id: llg.id
                    }))
                }))
            }))
        }))
    };

    console.log('Chart data:', chartData);

    // If D3 is not loaded, show a simple HTML tree
    if (typeof d3 === 'undefined') {
        console.log('D3 not loaded, showing HTML tree');
        showSimpleTree();
        return;
    }

    updateChart();
}

function updateChart() {
    console.log('updateChart called, chartData:', chartData);

    if (!chartData) {
        console.log('No chart data available');
        return;
    }

    const level = document.getElementById('chartLevel').value;
    const layout = document.getElementById('chartLayout').value;

    console.log('Chart level:', level, 'Layout:', layout);

    currentLevel = level;
    currentLayout = layout;

    // Filter data based on selected level
    const filteredData = filterDataByLevel(chartData, level);

    console.log('Filtered data:', filteredData);

    // Render chart based on layout
    if (layout === 'tree') {
        renderTreeChart(filteredData);
    } else if (layout === 'radial') {
        renderRadialChart(filteredData);
    } else {
        renderHorizontalChart(filteredData);
    }
}

function filterDataByLevel(data, level) {
    // Implementation would filter the data based on the selected level
    return data; // Simplified for now
}

function renderTreeChart(data) {
    console.log('Rendering tree chart with data:', data);

    try {
        // Clear the loading message first
        const container = document.getElementById('hierarchy-chart');
        container.innerHTML = '';

        // Create new SVG
        const width = container.clientWidth;
        const height = 600;

        console.log('Container dimensions:', width, height);

        const svg = d3.select('#hierarchy-chart')
            .append('svg')
            .attr('width', width)
            .attr('height', height)
            .style('background', 'linear-gradient(135deg, #1a1a2e, #16213e)')
            .style('border-radius', 'var(--radius-md)')
            .style('border', '1px solid rgba(255, 255, 255, 0.1)');

        const g = svg.append('g')
            .attr('class', 'chart-group');

        // Create tree layout with proper sizing
        const treeLayout = d3.tree().size([width - 200, height - 200]);
        const root = d3.hierarchy(data);

        console.log('Hierarchy root:', root);

        treeLayout(root);

        // Center the tree
        g.attr('transform', 'translate(100, 100)');

        // Draw links first
        const links = g.selectAll('.link')
            .data(root.links())
            .enter()
            .append('path')
            .attr('class', 'link')
            .attr('d', d3.linkVertical()
                .x(d => d.x)
                .y(d => d.y))
            .style('fill', 'none')
            .style('stroke', '#ffffff')
            .style('stroke-width', 2)
            .style('stroke-opacity', 0.6);

        console.log('Links drawn:', links.size());

        // Draw nodes
        const nodes = g.selectAll('.node')
            .data(root.descendants())
            .enter()
            .append('g')
            .attr('class', 'node')
            .attr('transform', d => `translate(${d.x}, ${d.y})`)
            .style('cursor', 'pointer')
            .on('click', function(event, d) {
                console.log('Node clicked:', d.data);
            });

        // Add circles for nodes (skip root)
        nodes.filter(d => d.data.type !== 'root')
            .append('circle')
            .attr('r', 12)
            .style('fill', d => getNodeColor(d.data.type))
            .style('stroke', '#ffffff')
            .style('stroke-width', 3);

        // Add text labels
        nodes.filter(d => d.data.type !== 'root')
            .append('text')
            .attr('dy', -20)
            .attr('text-anchor', 'middle')
            .style('fill', '#ffffff')
            .style('font-size', '12px')
            .style('font-weight', 'bold')
            .style('text-shadow', '1px 1px 2px rgba(0,0,0,0.7)')
            .text(d => d.data.name);

        console.log('Nodes drawn:', nodes.size());

        // Debug: Log node positions
        nodes.each(function(d, i) {
            console.log(`Node ${i}: ${d.data.name} at (${d.x}, ${d.y})`);
        });

        // Add zoom behavior
        const zoom = d3.zoom()
            .scaleExtent([0.1, 3])
            .on('zoom', function(event) {
                g.attr('transform', `translate(100, 100) ${event.transform}`);
            });

        svg.call(zoom);

    } catch (error) {
        console.error('Error rendering D3 chart:', error);
        showSimpleTree();
    }
}

function renderRadialChart(data) {
    // Radial chart implementation
    renderTreeChart(data); // Simplified for now
}

function renderHorizontalChart(data) {
    // Horizontal chart implementation
    renderTreeChart(data); // Simplified for now
}

function getNodeColor(type) {
    switch(type) {
        case 'country': return '#FF006E';
        case 'province': return '#00D4FF';
        case 'district': return '#FFB700';
        case 'llg': return '#06FFA5';
        default: return '#B8BCC8';
    }
}

function resetZoom() {
    const svg = d3.select('#hierarchy-chart svg');
    if (svg.node()) {
        svg.transition().duration(750).call(
            d3.zoom().transform,
            d3.zoomIdentity
        );
    }
}

function expandAll() {
    // Implementation to expand all nodes
    updateChart();
}

function collapseAll() {
    // Implementation to collapse all nodes
    updateChart();
}

function showSimpleTree() {
    const container = document.getElementById('hierarchy-chart');
    let html = '<div style="padding: 20px; overflow-y: auto; height: 100%;">';

    if (chartData && chartData.children) {
        chartData.children.forEach(country => {
            html += `
                <div style="margin-bottom: 20px; border: 2px solid #FF006E; border-radius: 8px; padding: 15px; background: rgba(255, 0, 110, 0.1);">
                    <h3 style="margin: 0 0 10px 0; color: #FF006E;">🌍 ${country.name}</h3>
            `;

            if (country.children && country.children.length > 0) {
                country.children.forEach(province => {
                    html += `
                        <div style="margin: 10px 0 10px 20px; border: 1px solid #00D4FF; border-radius: 6px; padding: 10px; background: rgba(0, 212, 255, 0.1);">
                            <h4 style="margin: 0 0 8px 0; color: #00D4FF;">🏛️ ${province.name}</h4>
                    `;

                    if (province.children && province.children.length > 0) {
                        province.children.forEach(district => {
                            html += `
                                <div style="margin: 8px 0 8px 20px; border: 1px solid #FFB700; border-radius: 4px; padding: 8px; background: rgba(255, 183, 0, 0.1);">
                                    <h5 style="margin: 0 0 5px 0; color: #FFB700;">🏢 ${district.name}</h5>
                            `;

                            if (district.children && district.children.length > 0) {
                                district.children.forEach(llg => {
                                    html += `
                                        <div style="margin: 5px 0 5px 20px; padding: 5px; border-left: 3px solid #06FFA5; background: rgba(6, 255, 165, 0.1);">
                                            <span style="color: #06FFA5;">🏘️ ${llg.name}</span>
                                        </div>
                                    `;
                                });
                            }

                            html += '</div>';
                        });
                    }

                    html += '</div>';
                });
            }

            html += '</div>';
        });
    }

    html += '</div>';
    container.innerHTML = html;
}

function exportChart() {
    // Implementation to export chart as image
    alert('Chart export functionality will be implemented.');
}
</script>
<?= $this->endSection() ?>