<?= $this->extend('templates/dakoii_portal_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('dakoii/government') ?>" class="btn btn-secondary">
    <i class="icon">←</i> Back to Overview
</a>
<button class="btn btn-primary" onclick="exportChart()">
    <i class="icon">📤</i> Export Chart
</button>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Page Header -->
    <div style="margin-bottom: var(--spacing-xl);">
        <h1 style="color: var(--text-primary); margin-bottom: var(--spacing-sm); font-size: 2rem; display: flex; align-items: center; gap: var(--spacing-md);">
            <span style="font-size: 2.5rem;">📊</span>
            Government Hierarchy Chart
        </h1>
        <p style="color: var(--text-secondary); font-size: 1rem;">
            Interactive visualization of the complete government structure hierarchy.
        </p>
    </div>

    <!-- Chart Controls -->
    <div class="card" style="margin-bottom: var(--spacing-xl);">
        <div class="card-header">Chart Controls</div>
        <div style="display: flex; gap: var(--spacing-md); align-items: center; flex-wrap: wrap;">
            <div style="display: flex; align-items: center; gap: var(--spacing-sm);">
                <label class="form-label" style="margin: 0;">View Level:</label>
                <select id="chartLevel" class="form-input" style="width: auto;" onchange="updateChart()">
                    <option value="all">All Levels</option>
                    <option value="countries">Countries Only</option>
                    <option value="provinces">Countries → Provinces</option>
                    <option value="districts">Countries → Provinces → Districts</option>
                    <option value="llgs">Complete Hierarchy</option>
                </select>
            </div>
            <button class="btn btn-secondary" onclick="resetZoom()">
                <i class="icon">🔍</i> Reset Zoom
            </button>
            <button class="btn btn-secondary" onclick="expandAll()">
                <i class="icon">📂</i> Expand All
            </button>
            <button class="btn btn-secondary" onclick="collapseAll()">
                <i class="icon">📁</i> Collapse All
            </button>
        </div>
    </div>

    <!-- Hierarchy Chart -->
    <div class="card">
        <div class="card-header">Interactive Hierarchy Chart</div>
        <div id="tree-container" style="height: 600px; position: relative; overflow: hidden; border-radius: var(--radius-md); background: linear-gradient(135deg, #1a1a2e, #16213e); border: 2px solid rgba(255, 255, 255, 0.1);">
            <div class="zoom-controls" style="position: absolute; top: 10px; right: 10px; display: flex; flex-direction: column; gap: 5px; z-index: 100;">
                <div class="zoom-btn" onclick="zoomIn()" style="width: 40px; height: 40px; background: rgba(0, 212, 255, 0.2); border: 2px solid #00D4FF; border-radius: 50%; cursor: pointer; display: flex; align-items: center; justify-content: center; font-size: 18px; font-weight: bold; color: #00D4FF; transition: all 0.3s ease;">+</div>
                <div class="zoom-btn" onclick="zoomOut()" style="width: 40px; height: 40px; background: rgba(0, 212, 255, 0.2); border: 2px solid #00D4FF; border-radius: 50%; cursor: pointer; display: flex; align-items: center; justify-content: center; font-size: 18px; font-weight: bold; color: #00D4FF; transition: all 0.3s ease;">−</div>
            </div>
            <!-- Chart will be rendered here -->
            <div id="loading-message" style="display: flex; align-items: center; justify-content: center; height: 100%; color: var(--text-tertiary);">
                <div style="text-align: center;">
                    <div style="font-size: 4rem; margin-bottom: var(--spacing-md);">📊</div>
                    <h3 style="color: var(--text-primary); margin-bottom: var(--spacing-sm);">Loading Hierarchy Chart...</h3>
                    <p>Interactive government structure visualization will appear here.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Chart Legend -->
    <div class="card">
        <div class="card-header">Chart Legend</div>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: var(--spacing-md);">
            <div style="display: flex; align-items: center; gap: var(--spacing-sm);">
                <div style="width: 20px; height: 20px; background: var(--gradient-primary); border-radius: 50%;"></div>
                <span style="color: var(--text-secondary);">Countries</span>
            </div>
            <div style="display: flex; align-items: center; gap: var(--spacing-sm);">
                <div style="width: 20px; height: 20px; background: var(--gradient-secondary); border-radius: 50%;"></div>
                <span style="color: var(--text-secondary);">Provinces</span>
            </div>
            <div style="display: flex; align-items: center; gap: var(--spacing-sm);">
                <div style="width: 20px; height: 20px; background: var(--gradient-accent); border-radius: 50%;"></div>
                <span style="color: var(--text-secondary);">Districts</span>
            </div>
            <div style="display: flex; align-items: center; gap: var(--spacing-sm);">
                <div style="width: 20px; height: 20px; background: #06FFA5; border-radius: 50%;"></div>
                <span style="color: var(--text-secondary);">Local Level Governments</span>
            </div>
        </div>
    </div>
</div>

<style>
/* Family Tree Chart Styles */
.node {
    cursor: pointer;
    transition: all 0.3s ease;
}

.node-rect {
    stroke-width: 2;
    rx: 8;
    ry: 8;
    filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1));
    transition: all 0.3s ease;
}

.node:hover .node-rect {
    filter: drop-shadow(0 6px 12px rgba(0, 0, 0, 0.2));
    transform: scale(1.05);
}

.node-text {
    font-family: var(--font-primary);
    font-weight: 600;
    text-anchor: middle;
    dominant-baseline: middle;
    pointer-events: none;
}

.node-subtitle {
    font-family: var(--font-primary);
    font-weight: 400;
    text-anchor: middle;
    dominant-baseline: middle;
    font-size: 11px;
    opacity: 0.8;
    pointer-events: none;
}

.link {
    fill: none;
    stroke: #00D4FF;
    stroke-width: 2;
    stroke-opacity: 0.8;
    transition: all 0.3s ease;
}

.link:hover {
    stroke-width: 3;
    stroke-opacity: 1;
}

/* Node colors by type */
.country-node { fill: #FF006E; }
.province-node { fill: #00D4FF; }
.district-node { fill: #FFB700; }
.llg-node { fill: #06FFA5; }

.country-text { fill: white; font-size: 14px; font-weight: bold; }
.province-text { fill: white; font-size: 13px; }
.district-text { fill: white; font-size: 12px; }
.llg-text { fill: #2c3e50; font-size: 11px; }

.zoom-btn:hover {
    background: #00D4FF !important;
    color: white !important;
    transform: scale(1.1);
}

.collapse-indicator {
    fill: rgba(255, 255, 255, 0.8);
    stroke: none;
    font-family: var(--font-primary);
    font-size: 16px;
    font-weight: bold;
    text-anchor: middle;
    dominant-baseline: middle;
    pointer-events: none;
}

.tooltip {
    position: absolute;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    pointer-events: none;
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.3s ease;
}
</style>

<script src="https://d3js.org/d3.v7.min.js"></script>
<script>
// Family Tree Style Government Hierarchy Chart
let chartData = null;
let root = null;
let svg = null;
let g = null;
let tree = null;
let zoom = null;
let i = 0;

document.addEventListener('DOMContentLoaded', function() {
    loadChartData();
});

function initializeChart() {
    const container = d3.select("#tree-container");
    const containerRect = container.node().getBoundingClientRect();
    const margin = { top: 40, right: 40, bottom: 40, left: 40 };
    const width = containerRect.width - margin.left - margin.right;
    const height = containerRect.height - margin.top - margin.bottom;

    // Remove loading message
    d3.select("#loading-message").remove();

    // Create SVG
    svg = container.append("svg")
        .attr("width", containerRect.width)
        .attr("height", containerRect.height);

    g = svg.append("g")
        .attr("transform", `translate(${margin.left},${margin.top})`);

    // Create tree layout
    tree = d3.tree()
        .size([width, height])
        .separation((a, b) => {
            return (a.parent === b.parent ? 1 : 1.2) *
                   (Math.max(getNodeWidth(a), getNodeWidth(b)) / 100);
        });

    // Create zoom behavior
    zoom = d3.zoom()
        .scaleExtent([0.1, 3])
        .on("zoom", (event) => {
            g.attr("transform",
                `translate(${margin.left + event.transform.x},${margin.top + event.transform.y}) scale(${event.transform.k})`
            );
        });

    svg.call(zoom);

    return { width, height, margin };
}

function loadChartData() {
    // Use real data from the controller
    const countries = <?= json_encode($countries ?? []) ?>;

    console.log('Countries data:', countries);

    // Check if we have data
    if (!countries || countries.length === 0) {
        console.log('No countries data available');
        // Show a message instead of empty chart
        document.getElementById('tree-container').innerHTML = `
            <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: var(--text-tertiary);">
                <div style="text-align: center;">
                    <div style="font-size: 4rem; margin-bottom: var(--spacing-md);">📊</div>
                    <h3 style="color: var(--text-primary); margin-bottom: var(--spacing-sm);">No Data Available</h3>
                    <p>Please add countries, provinces, districts, and LLGs to view the hierarchy chart.</p>
                    <a href="<?= base_url('dakoii/government/countries/create') ?>" class="btn btn-primary" style="margin-top: var(--spacing-md);">
                        Add Country
                    </a>
                </div>
            </div>
        `;
        return;
    }

    // Create hierarchical structure - if only one country, use it as root
    if (countries.length === 1) {
        chartData = {
            name: countries[0].name,
            type: "country",
            id: countries[0].id,
            children: (countries[0].provinces || []).map(province => ({
                name: province.name,
                type: "province",
                id: province.id,
                children: (province.districts || []).map(district => ({
                    name: district.name,
                    type: "district",
                    id: district.id,
                    children: (district.llgs || []).map(llg => ({
                        name: llg.name,
                        type: "llg",
                        id: llg.id
                    }))
                }))
            }))
        };
    } else {
        // Multiple countries - create a root node
        chartData = {
            name: "Government Structure",
            type: "root",
            children: countries.map(country => ({
                name: country.name,
                type: "country",
                id: country.id,
                children: (country.provinces || []).map(province => ({
                    name: province.name,
                    type: "province",
                    id: province.id,
                    children: (province.districts || []).map(district => ({
                        name: district.name,
                        type: "district",
                        id: district.id,
                        children: (district.llgs || []).map(llg => ({
                            name: llg.name,
                            type: "llg",
                            id: llg.id
                        }))
                    }))
                }))
            }))
        };
    }

    console.log('Chart data:', chartData);

    // If D3 is not loaded, show a simple HTML tree
    if (typeof d3 === 'undefined') {
        console.log('D3 not loaded, showing HTML tree');
        showSimpleTree();
        return;
    }

    initializeChart();
    renderFamilyTree();
}

// Helper functions
function getNodeWidth(d) {
    const lengths = {
        'root': 0,
        'country': 140,
        'province': 120,
        'district': 110,
        'llg': 100
    };
    return lengths[d.data.type] || 100;
}

function getNodeHeight(d) {
    return d.data.type === 'root' ? 0 : 50;
}

function getNodeColor(type) {
    const colors = {
        'root': 'transparent',
        'country': '#FF006E',
        'province': '#00D4FF',
        'district': '#FFB700',
        'llg': '#06FFA5'
    };
    return colors[type] || '#3498db';
}

function getTextColor(type) {
    return type === 'llg' ? '#2c3e50' : 'white';
}

function renderFamilyTree() {
    if (!chartData) return;

    root = d3.hierarchy(chartData);
    root.x0 = 0;
    root.y0 = 0;

    // Collapse some nodes initially for better display
    if (root.children) {
        root.children.forEach(d => {
            if (d.children) {
                d.children.forEach(child => {
                    if (child.children) {
                        child._children = child.children;
                        child.children = null;
                    }
                });
            }
        });
    }

    update(root);
}

function filterDataByLevel(data, level) {
    if (!data) return null;

    const cloneData = JSON.parse(JSON.stringify(data));

    switch(level) {
        case 'countries':
            // Show only countries (no children)
            if (cloneData.children) {
                cloneData.children = cloneData.children.map(country => ({
                    ...country,
                    children: undefined
                }));
            }
            break;

        case 'provinces':
            // Show countries and provinces only
            if (cloneData.children) {
                cloneData.children = cloneData.children.map(country => ({
                    ...country,
                    children: country.children ? country.children.map(province => ({
                        ...province,
                        children: undefined
                    })) : undefined
                }));
            }
            break;

        case 'districts':
            // Show countries, provinces, and districts
            if (cloneData.children) {
                cloneData.children = cloneData.children.map(country => ({
                    ...country,
                    children: country.children ? country.children.map(province => ({
                        ...province,
                        children: province.children ? province.children.map(district => ({
                            ...district,
                            children: undefined
                        })) : undefined
                    })) : undefined
                }));
            }
            break;

        case 'llgs':
        case 'all':
        default:
            // Show complete hierarchy - no filtering needed
            break;
    }

    return cloneData;
}

function update(source) {
    const duration = 750;

    // Compute the new tree layout
    const treeData = tree(root);
    const nodes = treeData.descendants();
    const links = treeData.descendants().slice(1);

    // Normalize for fixed-depth
    nodes.forEach(d => {
        d.y = d.depth * 120;
    });

    // Update the nodes
    const node = g.selectAll('g.node')
        .data(nodes, d => d.id || (d.id = ++i));

    // Enter any new nodes at the parent's previous position
    const nodeEnter = node.enter().append('g')
        .attr('class', 'node')
        .attr('transform', d => `translate(${source.x0},${source.y0})`)
        .on('click', click);

    // Add rectangles for the nodes
    nodeEnter.filter(d => d.data.type !== 'root')
        .append('rect')
        .attr('class', d => `node-rect ${d.data.type}-node`)
        .attr('width', d => getNodeWidth(d))
        .attr('height', d => getNodeHeight(d))
        .attr('x', d => -getNodeWidth(d) / 2)
        .attr('y', d => -getNodeHeight(d) / 2)
        .style('fill', d => getNodeColor(d.data.type))
        .style('stroke', '#fff')
        .style('stroke-width', 2);

    // Add labels for the nodes
    nodeEnter.filter(d => d.data.type !== 'root')
        .append('text')
        .attr('class', d => `node-text ${d.data.type}-text`)
        .attr('dy', '-5')
        .style('fill', d => getTextColor(d.data.type))
        .style('font-size', d => d.data.type === 'country' ? '14px' : '12px')
        .text(d => d.data.name);

    // Add subtitle (type) for the nodes
    nodeEnter.filter(d => d.data.type !== 'root')
        .append('text')
        .attr('class', 'node-subtitle')
        .attr('dy', '10')
        .style('fill', d => getTextColor(d.data.type))
        .style('opacity', 0.7)
        .text(d => d.data.type.toUpperCase());

    // Add collapse/expand indicator
    nodeEnter.filter(d => d.data.type !== 'root' && (d.children || d._children))
        .append('circle')
        .attr('class', 'collapse-indicator')
        .attr('r', 10)
        .attr('cy', 35)
        .style('fill', 'rgba(255, 255, 255, 0.8)')
        .style('stroke', '#333')
        .style('stroke-width', 1);

    nodeEnter.filter(d => d.data.type !== 'root' && (d.children || d._children))
        .append('text')
        .attr('class', 'collapse-indicator')
        .attr('dy', 40)
        .style('font-size', '12px')
        .style('fill', '#333')
        .text(d => d.children ? '−' : '+');

    // Transition nodes to their new position
    const nodeUpdate = nodeEnter.merge(node);

    nodeUpdate.transition()
        .duration(duration)
        .attr('transform', d => `translate(${d.x},${d.y})`);

    // Update the node attributes and style
    nodeUpdate.select('.collapse-indicator text')
        .text(d => d.children ? '−' : '+');

    // Remove any exiting nodes
    const nodeExit = node.exit().transition()
        .duration(duration)
        .attr('transform', d => `translate(${source.x},${source.y})`)
        .remove();

    nodeExit.select('rect')
        .attr('width', 0)
        .attr('height', 0);

    nodeExit.select('text')
        .style('fill-opacity', 0);

    // Update the links
    const link = g.selectAll('path.link')
        .data(links, d => d.id);

    // Enter any new links at the parent's previous position
    const linkEnter = link.enter().insert('path', 'g')
        .attr('class', 'link')
        .attr('d', d => {
            const o = { x: source.x0, y: source.y0 };
            return diagonal(o, o);
        });

    // Transition links to their new position
    const linkUpdate = linkEnter.merge(link);

    linkUpdate.transition()
        .duration(duration)
        .attr('d', d => diagonal(d, d.parent));

    // Remove any exiting links
    link.exit().transition()
        .duration(duration)
        .attr('d', d => {
            const o = { x: source.x, y: source.y };
            return diagonal(o, o);
        })
        .remove();

    // Store the old positions for transition
    nodes.forEach(d => {
        d.x0 = d.x;
        d.y0 = d.y;
    });
}

function renderRadialChart(data) {
    // Radial chart implementation
    renderTreeChart(data); // Simplified for now
}

function renderHorizontalChart(data) {
    // Horizontal chart implementation
    renderTreeChart(data); // Simplified for now
}

function getNodeColor(type) {
    switch(type) {
        case 'country': return '#FF006E';
        case 'province': return '#00D4FF';
        case 'district': return '#FFB700';
        case 'llg': return '#06FFA5';
        default: return '#B8BCC8';
    }
}

function resetZoom() {
    if (svg) {
        svg.transition().duration(750).call(
            zoom.transform,
            d3.zoomIdentity
        );
    }
}

function expandAll() {
    function expand(d) {
        if (d._children) {
            d.children = d._children;
            d._children = null;
        }
        if (d.children) {
            d.children.forEach(expand);
        }
    }
    if (root) {
        expand(root);
        update(root);
    }
}

function collapseAll() {
    function collapse(d) {
        if (d.children) {
            d._children = d.children;
            d.children = null;
            d._children.forEach(collapse);
        }
    }
    if (root && root.children) {
        root.children.forEach(collapse);
        update(root);
    }
}

// Click handler for nodes
function click(event, d) {
    if (d.children) {
        d._children = d.children;
        d.children = null;
    } else {
        d.children = d._children;
        d._children = null;
    }
    update(d);
}

// Diagonal path generator
function diagonal(s, d) {
    const path = `M ${s.x} ${s.y}
                  C ${s.x} ${(s.y + d.y) / 2},
                    ${d.x} ${(s.y + d.y) / 2},
                    ${d.x} ${d.y}`;
    return path;
}

// Zoom functions
function zoomIn() {
    if (svg) {
        svg.transition().call(zoom.scaleBy, 1.5);
    }
}

function zoomOut() {
    if (svg) {
        svg.transition().call(zoom.scaleBy, 1 / 1.5);
    }
}

function showSimpleTree() {
    const container = document.getElementById('hierarchy-chart');
    let html = '<div style="padding: 20px; overflow-y: auto; height: 100%;">';

    if (chartData && chartData.children) {
        chartData.children.forEach(country => {
            html += `
                <div style="margin-bottom: 20px; border: 2px solid #FF006E; border-radius: 8px; padding: 15px; background: rgba(255, 0, 110, 0.1);">
                    <h3 style="margin: 0 0 10px 0; color: #FF006E;">🌍 ${country.name}</h3>
            `;

            if (country.children && country.children.length > 0) {
                country.children.forEach(province => {
                    html += `
                        <div style="margin: 10px 0 10px 20px; border: 1px solid #00D4FF; border-radius: 6px; padding: 10px; background: rgba(0, 212, 255, 0.1);">
                            <h4 style="margin: 0 0 8px 0; color: #00D4FF;">🏛️ ${province.name}</h4>
                    `;

                    if (province.children && province.children.length > 0) {
                        province.children.forEach(district => {
                            html += `
                                <div style="margin: 8px 0 8px 20px; border: 1px solid #FFB700; border-radius: 4px; padding: 8px; background: rgba(255, 183, 0, 0.1);">
                                    <h5 style="margin: 0 0 5px 0; color: #FFB700;">🏢 ${district.name}</h5>
                            `;

                            if (district.children && district.children.length > 0) {
                                district.children.forEach(llg => {
                                    html += `
                                        <div style="margin: 5px 0 5px 20px; padding: 5px; border-left: 3px solid #06FFA5; background: rgba(6, 255, 165, 0.1);">
                                            <span style="color: #06FFA5;">🏘️ ${llg.name}</span>
                                        </div>
                                    `;
                                });
                            }

                            html += '</div>';
                        });
                    }

                    html += '</div>';
                });
            }

            html += '</div>';
        });
    }

    html += '</div>';
    container.innerHTML = html;
}

function updateChart() {
    const level = document.getElementById('chartLevel').value;

    if (!chartData) {
        console.log('No chart data available');
        return;
    }

    // Filter data based on selected level
    const filteredData = filterDataByLevel(chartData, level);

    if (!filteredData) {
        console.log('No filtered data available');
        return;
    }

    // Clear existing chart
    if (svg) {
        svg.remove();
    }

    // Update the global chartData temporarily for rendering
    const originalData = chartData;
    chartData = filteredData;

    // Re-initialize and render chart
    initializeChart();
    renderFamilyTree();

    // Restore original data
    chartData = originalData;
}

function zoomIn() {
    if (svg && zoom) {
        svg.transition().duration(300).call(
            zoom.scaleBy, 1.5
        );
    }
}

function zoomOut() {
    if (svg && zoom) {
        svg.transition().duration(300).call(
            zoom.scaleBy, 1 / 1.5
        );
    }
}

function resetZoom() {
    if (svg && zoom) {
        svg.transition().duration(500).call(
            zoom.transform,
            d3.zoomIdentity
        );
    }
}

function expandAll() {
    if (!root) return;

    function expand(d) {
        if (d._children) {
            d.children = d._children;
            d._children = null;
        }
        if (d.children) {
            d.children.forEach(expand);
        }
    }

    expand(root);
    update(root);
}

function collapseAll() {
    if (!root) return;

    function collapse(d) {
        if (d.children) {
            d._children = d.children;
            d.children = null;
        }
        if (d._children) {
            d._children.forEach(collapse);
        }
    }

    if (root.children) {
        root.children.forEach(collapse);
    }
    update(root);
}

function exportChart() {
    // Implementation to export chart as image
    alert('Chart export functionality will be implemented.');
}
</script>
<?= $this->endSection() ?>